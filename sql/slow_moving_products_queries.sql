-- Queries for finding slow moving products
-- Products that had stock throughout the period AND were never ordered

-- =====================================================
-- APPROACH 1: Most comprehensive (with detailed stats)
-- =====================================================
SELECT DISTINCT ssl.buxus_product_id,
       MIN(ssl.stock_value) as min_stock,
       MAX(ssl.stock_value) as max_stock,
       AVG(ssl.stock_value) as avg_stock,
       COUNT(*) as stock_changes
FROM stock_state_log ssl
WHERE ssl.created_at BETWEEN '2024-01-01' AND '2024-01-31'
  -- Condition 1: Never had zero stock during the period
  AND NOT EXISTS (
    SELECT 1 
    FROM stock_state_log ssl2 
    WHERE ssl2.buxus_product_id = ssl.buxus_product_id
      AND ssl2.created_at BETWEEN '2024-01-01' AND '2024-01-31'
      AND ssl2.stock_value = 0
  )
  -- Condition 2: Never ordered during the period
  AND NOT EXISTS (
    SELECT 1
    FROM tblShopOrderItems soi
    INNER JOIN tblShopOrders so ON soi.order_id = so.order_id
    WHERE soi.page_id = ssl.buxus_product_id
      AND so.order_datetime BETWEEN '2024-01-01' AND '2024-01-31'
  )
GROUP BY ssl.buxus_product_id
ORDER BY avg_stock DESC;

-- =====================================================
-- APPROACH 2: Simpler and faster (using HAVING)
-- =====================================================
SELECT ssl.buxus_product_id,
       MIN(ssl.stock_value) as min_stock,
       MAX(ssl.stock_value) as max_stock,
       AVG(ssl.stock_value) as avg_stock,
       COUNT(*) as stock_changes
FROM stock_state_log ssl
WHERE ssl.created_at BETWEEN '2024-01-01' AND '2024-01-31'
  -- Never ordered during the period
  AND NOT EXISTS (
    SELECT 1
    FROM tblShopOrderItems soi
    INNER JOIN tblShopOrders so ON soi.order_id = so.order_id
    WHERE soi.page_id = ssl.buxus_product_id
      AND so.order_datetime BETWEEN '2024-01-01' AND '2024-01-31'
  )
GROUP BY ssl.buxus_product_id
-- Never had zero stock (using HAVING)
HAVING MIN(ssl.stock_value) > 0
ORDER BY avg_stock DESC;

-- =====================================================
-- APPROACH 3: Just product IDs (fastest)
-- =====================================================
SELECT DISTINCT ssl.buxus_product_id
FROM stock_state_log ssl
WHERE ssl.created_at BETWEEN '2024-01-01' AND '2024-01-31'
  AND NOT EXISTS (
    SELECT 1 
    FROM stock_state_log ssl2 
    WHERE ssl2.buxus_product_id = ssl.buxus_product_id
      AND ssl2.created_at BETWEEN '2024-01-01' AND '2024-01-31'
      AND ssl2.stock_value = 0
  )
  AND NOT EXISTS (
    SELECT 1
    FROM tblShopOrderItems soi
    INNER JOIN tblShopOrders so ON soi.order_id = so.order_id
    WHERE soi.page_id = ssl.buxus_product_id
      AND so.order_datetime BETWEEN '2024-01-01' AND '2024-01-31'
  )
ORDER BY ssl.buxus_product_id;

-- =====================================================
-- APPROACH 4: With product information (if needed)
-- =====================================================
SELECT DISTINCT ssl.buxus_product_id,
       p.page_name as product_name,
       MIN(ssl.stock_value) as min_stock,
       MAX(ssl.stock_value) as max_stock,
       AVG(ssl.stock_value) as avg_stock,
       COUNT(*) as stock_changes
FROM stock_state_log ssl
LEFT JOIN tblPages p ON ssl.buxus_product_id = p.page_id
WHERE ssl.created_at BETWEEN '2024-01-01' AND '2024-01-31'
  AND NOT EXISTS (
    SELECT 1 
    FROM stock_state_log ssl2 
    WHERE ssl2.buxus_product_id = ssl.buxus_product_id
      AND ssl2.created_at BETWEEN '2024-01-01' AND '2024-01-31'
      AND ssl2.stock_value = 0
  )
  AND NOT EXISTS (
    SELECT 1
    FROM tblShopOrderItems soi
    INNER JOIN tblShopOrders so ON soi.order_id = so.order_id
    WHERE soi.page_id = ssl.buxus_product_id
      AND so.order_datetime BETWEEN '2024-01-01' AND '2024-01-31'
  )
GROUP BY ssl.buxus_product_id, p.page_name
ORDER BY avg_stock DESC;

-- =====================================================
-- APPROACH 5: Count only (for quick overview)
-- =====================================================
SELECT COUNT(DISTINCT ssl.buxus_product_id) as slow_moving_products_count
FROM stock_state_log ssl
WHERE ssl.created_at BETWEEN '2024-01-01' AND '2024-01-31'
  AND NOT EXISTS (
    SELECT 1 
    FROM stock_state_log ssl2 
    WHERE ssl2.buxus_product_id = ssl.buxus_product_id
      AND ssl2.created_at BETWEEN '2024-01-01' AND '2024-01-31'
      AND ssl2.stock_value = 0
  )
  AND NOT EXISTS (
    SELECT 1
    FROM tblShopOrderItems soi
    INNER JOIN tblShopOrders so ON soi.order_id = so.order_id
    WHERE soi.page_id = ssl.buxus_product_id
      AND so.order_datetime BETWEEN '2024-01-01' AND '2024-01-31'
  );

-- =====================================================
-- PERFORMANCE TIPS:
-- =====================================================
-- 1. Make sure you have indexes on:
--    - stock_state_log.created_at
--    - stock_state_log.buxus_product_id
--    - tblShopOrders.order_datetime
--    - tblShopOrderItems.page_id
--    - tblShopOrderItems.order_id
--
-- 2. Use APPROACH 2 (with HAVING) for best balance of performance and readability
-- 3. Use APPROACH 3 if you only need product IDs
-- 4. Consider adding date range parameters as variables/parameters in your application
