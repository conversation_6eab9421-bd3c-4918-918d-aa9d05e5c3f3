<?php

// Usage examples for StockAnalysisService

use App\Services\StockAnalysisService;

$stockService = new StockAnalysisService();

// Example 1: Get product IDs that never had zero stock in the last month
$dateFrom = '2024-01-01';
$dateTo = '2024-01-31';

$productIds = $stockService->getProductsNeverZeroStock($dateFrom, $dateTo);
echo "Products that never had zero stock: " . $productIds->count() . "\n";

// Example 2: Get detailed stock history for products that never had zero stock
$stockHistory = $stockService->getProductsWithStockHistoryNeverZero($dateFrom, $dateTo);
foreach ($stockHistory as $record) {
    echo "Product {$record->buxus_product_id}: Stock {$record->stock_value} at {$record->created_at}\n";
}

// Example 3: Get products with their minimum stock values (all > 0)
$minStocks = $stockService->getProductsWithMinStockNeverZero($dateFrom, $dateTo);
foreach ($minStocks as $product) {
    echo "Product {$product->buxus_product_id}: Min stock {$product->min_stock}\n";
}

// Example 4: Using raw SQL for maximum performance
$products = $stockService->getProductsNeverZeroStockRaw($dateFrom, $dateTo);
foreach ($products as $product) {
    echo "Product ID: {$product->buxus_product_id}\n";
}

// Example 5: Direct Eloquent query
$productIds = StockStateLog::select('buxus_product_id')
    ->whereBetween('created_at', [$dateFrom, $dateTo])
    ->groupBy('buxus_product_id')
    ->having(DB::raw('MIN(stock_value)'), '>', 0)
    ->pluck('buxus_product_id');
