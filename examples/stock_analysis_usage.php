<?php

// Usage examples for StockAnalysisService

use App\Services\StockAnalysisService;

$stockService = new StockAnalysisService();

// Example 1: Get product IDs that never had zero stock in the last month
$dateFrom = '2024-01-01';
$dateTo = '2024-01-31';

$productIds = $stockService->getProductsNeverZeroStock($dateFrom, $dateTo);
echo "Products that never had zero stock: " . $productIds->count() . "\n";

// Example 2: Get detailed stock history for products that never had zero stock
$stockHistory = $stockService->getProductsWithStockHistoryNeverZero($dateFrom, $dateTo);
foreach ($stockHistory as $record) {
    echo "Product {$record->buxus_product_id}: Stock {$record->stock_value} at {$record->created_at}\n";
}

// Example 3: Get products with their minimum stock values (all > 0)
$minStocks = $stockService->getProductsWithMinStockNeverZero($dateFrom, $dateTo);
foreach ($minStocks as $product) {
    echo "Product {$product->buxus_product_id}: Min stock {$product->min_stock}\n";
}

// Example 4: Using raw SQL for maximum performance
$products = $stockService->getProductsNeverZeroStockRaw($dateFrom, $dateTo);
foreach ($products as $product) {
    echo "Product ID: {$product->buxus_product_id}\n";
}

// Example 5: Direct Eloquent query
$productIds = StockStateLog::select('buxus_product_id')
    ->whereBetween('created_at', [$dateFrom, $dateTo])
    ->groupBy('buxus_product_id')
    ->having(DB::raw('MIN(stock_value)'), '>', 0)
    ->pluck('buxus_product_id');

// Example 6: Get slow moving products (had stock but never ordered)
$slowMovingProducts = $stockService->getSlowMovingProducts($dateFrom, $dateTo);
echo "Slow moving products count: " . $slowMovingProducts->count() . "\n";

// Example 7: Get slow moving products with detailed stock information
$slowMovingDetails = $stockService->getSlowMovingProductsWithDetails($dateFrom, $dateTo);
foreach ($slowMovingDetails as $product) {
    echo "Product {$product->buxus_product_id}: ";
    echo "Min: {$product->min_stock}, Max: {$product->max_stock}, ";
    echo "Avg: " . round($product->avg_stock, 2) . ", Changes: {$product->stock_changes}\n";
}

// Example 8: Raw SQL for best performance with slow moving products
$slowMovingRaw = $stockService->getSlowMovingProductsRaw($dateFrom, $dateTo);
foreach ($slowMovingRaw as $product) {
    echo "Product {$product->buxus_product_id}: ";
    echo "Avg stock: " . round($product->avg_stock, 2) . "\n";
}

// Example 9: Simple approach using GROUP BY and HAVING
$slowMovingSimple = $stockService->getSlowMovingProductsSimple($dateFrom, $dateTo);
foreach ($slowMovingSimple as $product) {
    echo "Product {$product->buxus_product_id}: ";
    echo "Min: {$product->min_stock}, Avg: " . round($product->avg_stock, 2) . "\n";
}
