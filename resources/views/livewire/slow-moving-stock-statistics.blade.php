<div>
    <div class="ibox">
        <div class="ibox-title">
            <h5>Vyhľadávanie</h5>
        </div>
        <div class="ibox-content">
            <div class="my-2">
                <div class="d-flex">
                    <form class="d-flex w-100">
                        <input type="date" class="form-control mx-2"
                               value="{{ request('date_from') }}"
                               name="date_from"
                               wire:model="dateFrom">
                        <input type="date" class="form-control mx-2"
                               value="{{ request('date_to') }}"
                               name="date_to"
                               wire:model="dateTo">
                        <button class="btn btn-primary mx-2" wire:click="export">Export</button>
                    </form>
                </div>
            </div>



    "Produkt bol cele sledovane obdobie skladom" --- trackovanie
        = mohol by som trackovat stav raz za den,
        = a navyse stav pri kazdej zmene. skladu == ked pride stav z onixu a je iny ako posledny zalogovany.



    budem vediet zvolit lubovolne sledovane obdobie (podla datumu) a nasledne si stiahnut zoznam dielov, ktore si nikto neobjednal pocas celeho tohto obdobia – teda nemali ziaden predaj.

    Plati to iba pre produkty z ONIX-u – musia mat priradene ONIX cislo.

    Zaroven je dolezite, aby boli splnene obe nasledujuce podmienky:
        - Produkt bol cele sledovane obdobie skladom (teda fyzicky dostupny),
        - Zaroven si ho nikto pocas tohto obdobia neobjednal.

    Vystupny subor by mal obsahovat nasledovne udaje:
    ONIX cislo dielu
    Hlavny kod
    Nazov produktu
    Vyrobca
    Pocet kusov na sklade
    Nakupna cena za kus

</div>
