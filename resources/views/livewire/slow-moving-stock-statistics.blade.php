<div>
    <div class="ibox">
        <div class="ibox-title">
            <h5>Vyhľadávanie</h5>
        </div>
        <div class="ibox-content">
            <div class="my-2">
                <div class="d-flex">
                    <form class="d-flex w-100">
                        <input type="date" class="form-control mx-2"
                               value="{{ request(\App\Http\Livewire\ExtendedSearchStatisticsShow::EXISTENT_DATE_FROM) }}"
                               name="{{ \App\Http\Livewire\ExtendedSearchStatisticsShow::EXISTENT_DATE_FROM }}"
                               wire:model="{{ \App\Http\Livewire\ExtendedSearchStatisticsShow::EXISTENT_DATE_FROM }}">
                        <input type="date" class="form-control mx-2"
                               value="{{ request(\App\Http\Livewire\ExtendedSearchStatisticsShow::EXISTENT_DATE_TO) }}"
                               name="{{ \App\Http\Livewire\ExtendedSearchStatisticsShow::EXISTENT_DATE_TO }}"
                               wire:model="{{ \App\Http\Livewire\ExtendedSearchStatisticsShow::EXISTENT_DATE_TO }}">
                        <button class="btn btn-primary mx-2">Filtrovať</button>
                    </form>
                    <button class="btn btn-primary mx-2" wire:click="export">Export</button>
                </div>
            </div>



    "Produkt bol cele sledovane obdobie skladom" --- trackovanie
        = mohol by som trackovat stav raz za den,
        = a navyse stav pri kazdej zmene. skladu == ked pride stav z onixu a je iny ako posledny zalogovany.



    budem vediet zvolit lubovolne sledovane obdobie (podla datumu) a nasledne si stiahnut zoznam dielov, ktore si nikto neobjednal pocas celeho tohto obdobia – teda nemali ziaden predaj.

    Plati to iba pre produkty z ONIX-u – musia mat priradene ONIX cislo.

    Zaroven je dolezite, aby boli splnene obe nasledujuce podmienky:
        - Produkt bol cele sledovane obdobie skladom (teda fyzicky dostupny),
        - Zaroven si ho nikto pocas tohto obdobia neobjednal.

    Vystupny subor by mal obsahovat nasledovne udaje:
    ONIX cislo dielu
    Hlavny kod
    Nazov produktu
    Vyrobca
    Pocet kusov na sklade
    Nakupna cena za kus

</div>
