<?php

namespace App\Services;

use App\Models\StockStateLog;
use Illuminate\Support\Facades\DB;

class StockAnalysisService
{
    /**
     * Get products that never had zero stock during a specific period
     *
     * @param string $dateFrom Start date (Y-m-d format)
     * @param string $dateTo End date (Y-m-d format)
     * @return \Illuminate\Support\Collection
     */
    public function getProductsNeverZeroStock($dateFrom, $dateTo)
    {
        return StockStateLog::select('buxus_product_id')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->whereNotExists(function ($query) use ($dateFrom, $dateTo) {
                $query->select(DB::raw(1))
                    ->from('stock_state_log as ssl2')
                    ->whereColumn('ssl2.buxus_product_id', 'stock_state_log.buxus_product_id')
                    ->whereBetween('ssl2.created_at', [$dateFrom, $dateTo])
                    ->where('ssl2.stock_value', 0);
            })
            ->distinct()
            ->pluck('buxus_product_id');
    }

    /**
     * Get products with their stock history that never had zero stock
     *
     * @param string $dateFrom Start date (Y-m-d format)
     * @param string $dateTo End date (Y-m-d format)
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getProductsWithStockHistoryNeverZero($dateFrom, $dateTo)
    {
        $productIds = $this->getProductsNeverZeroStock($dateFrom, $dateTo);

        return StockStateLog::whereIn('buxus_product_id', $productIds)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->orderBy('buxus_product_id')
            ->orderBy('created_at')
            ->get();
    }

    /**
     * Get products with minimum stock value during the period (never zero)
     *
     * @param string $dateFrom Start date (Y-m-d format)
     * @param string $dateTo End date (Y-m-d format)
     * @return \Illuminate\Support\Collection
     */
    public function getProductsWithMinStockNeverZero($dateFrom, $dateTo)
    {
        return DB::table('stock_state_log')
            ->select('buxus_product_id', DB::raw('MIN(stock_value) as min_stock'))
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->groupBy('buxus_product_id')
            ->having('min_stock', '>', 0)
            ->get();
    }

    /**
     * Raw SQL query for better performance with large datasets
     *
     * @param string $dateFrom Start date (Y-m-d format)
     * @param string $dateTo End date (Y-m-d format)
     * @return array
     */
    public function getProductsNeverZeroStockRaw($dateFrom, $dateTo)
    {
        $sql = "
            SELECT DISTINCT ssl.buxus_product_id
            FROM stock_state_log ssl
            WHERE ssl.created_at BETWEEN ? AND ?
              AND NOT EXISTS (
                SELECT 1
                FROM stock_state_log ssl2
                WHERE ssl2.buxus_product_id = ssl.buxus_product_id
                  AND ssl2.created_at BETWEEN ? AND ?
                  AND ssl2.stock_value = 0
              )
            ORDER BY ssl.buxus_product_id
        ";

        return DB::select($sql, [$dateFrom, $dateTo, $dateFrom, $dateTo]);
    }

    /**
     * Get products that had stock throughout the period AND were never ordered
     *
     * @param string $dateFrom Start date (Y-m-d format)
     * @param string $dateTo End date (Y-m-d format)
     * @return \Illuminate\Support\Collection
     */
    public function getSlowMovingProducts($dateFrom, $dateTo)
    {
        return StockStateLog::select('buxus_product_id')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            // Condition 1: Never had zero stock
            ->whereNotExists(function ($query) use ($dateFrom, $dateTo) {
                $query->select(DB::raw(1))
                    ->from('stock_state_log as ssl2')
                    ->whereColumn('ssl2.buxus_product_id', 'stock_state_log.buxus_product_id')
                    ->whereBetween('ssl2.created_at', [$dateFrom, $dateTo])
                    ->where('ssl2.stock_value', 0);
            })
            // Condition 2: Never ordered during the period
            ->whereNotExists(function ($query) use ($dateFrom, $dateTo) {
                $query->select(DB::raw(1))
                    ->from('tblShopOrderItems as soi')
                    ->join('tblShopOrders as so', 'soi.order_id', '=', 'so.order_id')
                    ->whereColumn('soi.page_id', 'stock_state_log.buxus_product_id')
                    ->whereBetween('so.order_datetime', [$dateFrom, $dateTo]);
            })
            ->distinct()
            ->pluck('buxus_product_id');
    }

    /**
     * Get slow moving products with detailed stock information
     *
     * @param string $dateFrom Start date (Y-m-d format)
     * @param string $dateTo End date (Y-m-d format)
     * @return \Illuminate\Support\Collection
     */
    public function getSlowMovingProductsWithDetails($dateFrom, $dateTo)
    {
        $productIds = $this->getSlowMovingProducts($dateFrom, $dateTo);

        return DB::table('stock_state_log')
            ->select(
                'buxus_product_id',
                DB::raw('MIN(stock_value) as min_stock'),
                DB::raw('MAX(stock_value) as max_stock'),
                DB::raw('AVG(stock_value) as avg_stock'),
                DB::raw('COUNT(*) as stock_changes')
            )
            ->whereIn('buxus_product_id', $productIds)
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->groupBy('buxus_product_id')
            ->orderBy('avg_stock', 'desc')
            ->get();
    }

    /**
     * Raw SQL query for slow moving products (best performance)
     *
     * @param string $dateFrom Start date (Y-m-d format)
     * @param string $dateTo End date (Y-m-d format)
     * @return array
     */
    public function getSlowMovingProductsRaw($dateFrom, $dateTo)
    {
        $sql = "
            SELECT DISTINCT ssl.buxus_product_id,
                   MIN(ssl.stock_value) as min_stock,
                   MAX(ssl.stock_value) as max_stock,
                   AVG(ssl.stock_value) as avg_stock,
                   COUNT(*) as stock_changes
            FROM stock_state_log ssl
            WHERE ssl.created_at BETWEEN ? AND ?
              -- Condition 1: Never had zero stock during the period
              AND NOT EXISTS (
                SELECT 1
                FROM stock_state_log ssl2
                WHERE ssl2.buxus_product_id = ssl.buxus_product_id
                  AND ssl2.created_at BETWEEN ? AND ?
                  AND ssl2.stock_value = 0
              )
              -- Condition 2: Never ordered during the period
              AND NOT EXISTS (
                SELECT 1
                FROM tblShopOrderItems soi
                INNER JOIN tblShopOrders so ON soi.order_id = so.order_id
                WHERE soi.page_id = ssl.buxus_product_id
                  AND so.order_datetime BETWEEN ? AND ?
              )
            GROUP BY ssl.buxus_product_id
            ORDER BY avg_stock DESC
        ";

        return DB::select($sql, [$dateFrom, $dateTo, $dateFrom, $dateTo, $dateFrom, $dateTo]);
    }

    /**
     * Alternative approach using GROUP BY and HAVING (simpler query)
     *
     * @param string $dateFrom Start date (Y-m-d format)
     * @param string $dateTo End date (Y-m-d format)
     * @return array
     */
    public function getSlowMovingProductsSimple($dateFrom, $dateTo)
    {
        $sql = "
            SELECT ssl.buxus_product_id,
                   MIN(ssl.stock_value) as min_stock,
                   MAX(ssl.stock_value) as max_stock,
                   AVG(ssl.stock_value) as avg_stock,
                   COUNT(*) as stock_changes
            FROM stock_state_log ssl
            WHERE ssl.created_at BETWEEN ? AND ?
              -- Never ordered during the period
              AND NOT EXISTS (
                SELECT 1
                FROM tblShopOrderItems soi
                INNER JOIN tblShopOrders so ON soi.order_id = so.order_id
                WHERE soi.page_id = ssl.buxus_product_id
                  AND so.order_datetime BETWEEN ? AND ?
              )
            GROUP BY ssl.buxus_product_id
            -- Never had zero stock (using HAVING)
            HAVING MIN(ssl.stock_value) > 0
            ORDER BY avg_stock DESC
        ";

        return DB::select($sql, [$dateFrom, $dateTo, $dateFrom, $dateTo]);
    }
}
