<?php

namespace App\Invoice\Tasks;

use App\Invoice\Facades\InvoiceFactory;
use Buxus\Util\PageIds;

class SendFirstEmailNotice
{
    public function handle()
    {
        $invoices = InvoiceFactory::getToBePaidInDays(5);

        $mailPageId = PageIds::getDueDateIn_5Days();

        foreach ($invoices as $invoice) {
            $users = $invoice->getUsers();
            if (!$invoice->wasEmailSent($mailPageId)) {
                $emails = [];

                foreach ($users as $user) {
                    if (!empty(filter_var($user->getEmail(), FILTER_VALIDATE_EMAIL))) {
                        $emails[] = $user->getEmail();
                    }
                    if (!empty(filter_var($user->getUsername(), FILTER_VALIDATE_EMAIL))) {
                        $emails[] = $user->getUsername();
                    }
                }

                $emails = array_unique($emails);
                if (!empty($emails)) {
                    $email = \Email::get($mailPageId);
                    $email->setRecipientsAddresses($emails);
                    $email->setDataTag('VARIABLE_SYMBOL', $invoice->vs);
                    $email->addAttachment($invoice->getPathForEmail(), $invoice->getFilenameForEmail());
                    $email->send();
                }

                $invoice->logEmail($mailPageId);
            }
        }
    }
}
