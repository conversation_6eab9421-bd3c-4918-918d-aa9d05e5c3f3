<?php

namespace App\Invoice;

use App\Models\EnclosureTransaction;
use App\Onix\Onix;
use App\WebUser\WebUser;
use Buxus\Util\PageIds;
use Buxus\Util\Url;
use Carbon\Carbon;
use DB;

class Invoice
{
    public const PAID = 'PAID';
    public const PARTLY_PAID = 'PARTLY_PAID';
    public const UNPAID = 'UNPAID';

    public const UPDATE_IN_PROGRESS = 0;
    public const UPDATE_DONE = 1;

    public const UPDATE_TAG = 'update';
    public const NOTICES_TAG = 'notices';

    protected $invoice;

    public function __construct($enclosureId)
    {
        $this->invoice = \DB::table('onix_enclosures')
            ->where('enclosure_record_id', $enclosureId)
            ->where('enclosure_type_id', Onix::INVOICE_TYPE_ID)
            ->first();
    }

    public function __get(string $key)
    {
        return $this->invoice->$key;
    }

    public function __set(string $key, mixed $value)
    {
        return $this->invoice->$key;
    }

    public function __isset(string $key)
    {
        return isset($this->invoice->$key);
    }

    public function __unset($key)
    {
        unset($this->invoice->key);
    }

    public function isValid()
    {
        return isset($this->invoice->enclosure_record_id) && is_numeric($this->invoice->enclosure_record_id);
    }

    public function getPaymentState()
    {
        if ($this->getPaymentRemaining() == 0) {
            return self::PAID;
        }

        if ($this->getPaymentStatus() == 0) {
            return self::UNPAID;
        }

        return self::PARTLY_PAID;
    }

    public function getPaymentRemaining()
    {
        return $this->invoice->payment_remaining;
    }

    public function getPaymentStatus()
    {
        return $this->invoice->payment_status;
    }

    public function getPaymentStateToShow()
    {
        $state = $this->getPaymentState();

        if ($state == self::PAID) {
            return view('invoice.states.paid');
        }

        $daysAfterDueDate = $this->getInvoiceAfterDueDateDiff() > 0
            ? \Trans::choice('invoice', '{1} (:count deň po splatnosti)|[2,4] (:count dni po splatnosti)|[5,*] (:count dní po splatnosti)', $this->getInvoiceAfterDueDateDiff())
            : '';

        if ($state == self::UNPAID) {
            return view('invoice.states.unpaid');
        }

        return view('invoice.states.partly-paid');
    }

    public function wasEmailSent($tag)
    {
        $lastEmail = \DB::table('tblInvoiceEmailRecords')
            ->where('enclosure_record_id', $this->enclosure_record_id)
            ->where('email_type_tag', $tag)
            ->orderBy('created_at')
            ->get();

        if ($lastEmail->isNotEmpty()) {
            return true;
        }

        return false;
    }

    public function logEmail($tag)
    {
        $record = DB::table('tblInvoiceEmailRecords')->insert([
            'enclosure_record_id' => $this->enclosure_record_id,
            'email_type_tag' => $tag,
            'created_at' => Carbon::now()->toDateTimeString(),
        ]);

        return $record;
    }

    public function shouldNotificate()
    {
        if ($this->getPaymentRemaining() > 0) {
            return true;
        }

        return false;
    }

    public function getInvoiceAfterDueDateDiff()
    {
        if ($this->getPaymentState() == static::PAID) {
            return;
        }

        $dueDate = Carbon::parse($this->due_date)->startOfDay();

        if ($dueDate < Carbon::now()) {
            return $dueDate->diffInDays(Carbon::now()->startOfDay());
        }

        return false;
    }

    public function getUsers()
    {
        if (empty($this->partner_id)) {
            return [];
        }

        $users = \DB::table('tblWebUserOptions')
            ->where('user_option_tag', 'onix_partner_id')
            ->where('user_option_value', $this->partner_id)
            ->get();

        $usersArr = [];

        foreach ($users as $user) {
            $usersArr[] = \WebUserFactory::getById($user->user_id);
        }

        return $usersArr;
    }

    public function getPathForEmail()
    {
        return storage_path('app/' . $this->path);
    }

    public function getFilenameForEmail()
    {
        return $this->vs . '.pdf';
    }

    public function offsetCreditNote(CreditNote $creditNote)
    {
        $transaction = $this->createTransaction($creditNote);
        $this->sendConfirmationMail($transaction);
    }

    public function processValuesOffsetting(CreditNote $creditNote)
    {
        \DB::table('onix_enclosures')
            ->where('enclosure_record_id', $this->invoice->enclosure_record_id)
            ->update([
                'payment_remaining' => $this->getPaymentRemaining() - $creditNote->creditNote->payment_remaining,
                'payment_status' => $this->getPaymentStatus() + $creditNote->creditNote->payment_remaining,
            ]);

        \DB::table('onix_enclosures')
            ->where('enclosure_record_id', $creditNote->creditNote->enclosure_record_id)
            ->update([
                'payment_remaining' => 0,
                'payment_status' => $creditNote->creditNote->payment_remaining,
            ]);

        $creditNote->creditNote->payment_remaining = 0;
        $this->invoice->payment_remaining = $this->invoice->payment_remaining - $creditNote->creditNote->payment_remaining;
    }

    protected function createTransaction(CreditNote $creditNote)
    {
        return EnclosureTransaction::create([
            'invoice_enclosure_record_id' => $this->invoice->enclosure_record_id,
            'credit_note_enclosure_record_id' => $creditNote->creditNote->enclosure_record_id,
            'invoice_vs' => $this->invoice->vs,
            'credit_note_vs' => $creditNote->creditNote->vs,
            'transaction_sum' => $creditNote->creditNote->payment_remaining,
            'verification_hash' => md5($this->invoice->vs . $creditNote->creditNote->vs . env('APP_KEY')),
            'status' => EnclosureTransaction::STATUS_REQUESTED,
        ]);
    }

    protected function sendConfirmationMail(EnclosureTransaction $transaction)
    {
        $currency = $this->curr;

        $users = $this->getUsers();

        $companyData = [];
        /** @var WebUser $user */
        foreach ($users as $user) {
            $companyData[$user->getCompanyName()] = $user->getInvoiceEmailAddress();
        }

        dispatch(function () use ($transaction, $currency, $companyData) {
            $confirmationUrl = Url::buildGetUrl(
                Url::staticUrl(
                    Url::page(PageIds::getCreditNoteOffsetConfirmation())
                ),
                [
                    'hash' => $transaction->verification_hash,
                    'transactionId' => $transaction->id
                ]
            );

            $denialUrl = Url::buildGetUrl(
                Url::staticUrl(
                    Url::page(PageIds::getCreditNoteOffsetDenial())
                ),
                [
                    'hash' => $transaction->verification_hash,
                    'transactionId' => $transaction->id
                ]
            );

            $email = \Email::get(PageIds::getCreditNoteOffset());
            $email->setDataTag('TRANSACTION_SUM', \PriceViewer::formatRawPrice($transaction->transaction_sum, $currency));

            $companyDataStr = '';
            foreach ($companyData as $company => $companyEmail) {
                $companyDataStr .= "$company (" . $companyEmail . ")<br>";
            }

            $email->setDataTag('COMPANY_DATA', $companyDataStr);

            $email->setDataTag('CREDIT_NOTE_VS', $transaction->credit_note_vs);
            $email->setDataTag('INVOICE_VS', $transaction->invoice_vs);
            $email->setDataTag('CONFIRMATION_URL', $confirmationUrl);
            $email->setDataTag('DENIAL_URL', $denialUrl);
            $email->send();
        });
    }

    public function hasOngoingTransaction(): bool
    {
        return EnclosureTransaction::where('invoice_enclosure_record_id', $this->enclosure_record_id)
            ->where('status', EnclosureTransaction::STATUS_REQUESTED)
            ->exists();
    }
}
