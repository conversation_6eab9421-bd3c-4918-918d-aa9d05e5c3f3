<?php

namespace App\Http\Livewire;

use Buxus\Livewire\Component;
use Carbon\Carbon;

class SlowMovingStockStatistics extends Component
{
    public $dateFrom;
    public $dateTo;

    public function mount()
    {
        $this->dateFrom = request('date_from') ?? '1900-01-01';;
        $this->dateTo = request('sate_to') ?? Carbon::now()->addDays(1)->toDateString();
    }

    public function render()
    {
        $from = request('date_from') ?? '1900-01-01';
        $to = request('date_to') ?? Carbon::now()->addDays(1)->toDateString();


        $data = [];

        return view('livewire.slow-moving-stock-statistics', $data);
    }
}
