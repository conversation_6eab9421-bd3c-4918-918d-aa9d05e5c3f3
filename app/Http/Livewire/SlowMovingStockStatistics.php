<?php

namespace App\Http\Livewire;

use App\Models\StockStateLog;
use Buxus\Livewire\Component;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class SlowMovingStockStatistics extends Component
{
    public $dateFrom;
    public $dateTo;

    public function mount()
    {
        $this->dateFrom = request('date_from') ?? Carbon::now()->subYears(1)->toDateString();
        $this->dateTo = request('date_to') ?? Carbon::now()->addDays(1)->toDateString();
    }

    public function render()
    {
        return view('livewire.slow-moving-stock-statistics');
    }

    public function export()
    {
        // cele obdobie skladom, to znamena ze za obdobie nemoze mat ani jeden nulovy stav
        StockStateLog::where
    }
}
